'use client'

import React, {
  useEffect, useRef, useState,
} from 'react';
import Konva from 'konva';
import autosize from 'autosize';
import { VectorImageStyleGroups } from '@/common/constants';
import toast from 'react-hot-toast';
import { useMixpanelEvent } from '@/common/utils/mixpanel/eventTriggers';
import Image from 'next/image';
import { useProjectContext } from '@/common/contexts/ProjectContext';
import { projectImageStorage } from '@/common/utils/projectImageStorage';
import {
  Button, TextArea,
} from '../../../atoms';
import { addCursorHandlers } from '../CanvasEditor';
import { useCanvasLoading } from '../CanvasLoadingContext';

interface VectorImagePanelProps {
  canvas: Konva.Stage | null;
  agentId?: string;
  planId?: string;
}

type VectorOptionType = {
  option: string;
  label: string;
  substyle?: string;
}

const ColorSelector = ({
  selectedColors,
  onColorsChange,
  maxColors = 5,
}: {
  selectedColors: string[];
  onColorsChange: (colors: string[]) => void;
  maxColors?: number;
}) => {
  const predefinedColors = [
    '#ffffff', '#f3f4f6', '#e5e7eb', '#000000', '#1f2937', '#ef4444',
    '#f97316', '#eab308', '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899',
    '#06b6d4', '#84cc16', '#f59e0b', '#10b981',
  ];

  const toggleColor = (color: string) => {
    if (selectedColors.includes(color)) {
      onColorsChange(selectedColors.filter(c => c !== color));
    } else if (selectedColors.length < maxColors) {
      onColorsChange([...selectedColors, color]);
    }
  };

  return (
    <div className="space-y-2">
      <div className="grid grid-cols-8 gap-2">
        {predefinedColors.map((color) => (
          <button
            key={color}
            onClick={() => toggleColor(color)}
            className={`w-6 h-6 rounded border-2 transition-all ${
              selectedColors.includes(color)
                ? 'border-violets-are-blue scale-110 ring-2 ring-violets-are-blue/50'
                : 'border-neutral-500 hover:border-neutral-400'
            }`}
            style={{ backgroundColor: color }}
            title={color}
          />
        ))}
      </div>
      {selectedColors.length > 0 && (
        <div className="text-xs text-gray-400">
          Selected: {selectedColors.length}/{maxColors} colors
        </div>
      )}
    </div>
  );
};

const BackgroundColorSelector = ({
  selectedColor,
  onColorChange,
}: {
  selectedColor: string;
  onColorChange: (color: string) => void;
}) => {
  const predefinedColors = [
    '#ffffff', '#f3f4f6', '#e5e7eb', '#000000', '#1f2937', '#ef4444',
    '#f97316', '#eab308', '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899',
    '#06b6d4', '#84cc16', '#f59e0b', '#10b981',
  ];

  return (
    <div className="space-y-2">
      <div className="grid grid-cols-8 gap-2">
        {/* Clear selection button */}
        <button
          onClick={() => onColorChange('')}
          className={`w-6 h-6 rounded border-2 transition-all flex items-center justify-center ${
            !selectedColor
              ? 'border-violets-are-blue scale-110 ring-2 ring-violets-are-blue/50'
              : 'border-neutral-500 hover:border-neutral-400'
          }`}
          style={{ backgroundColor: '#transparent' }}
          title="No background color"
        >
          <span className="text-xs text-gray-400">×</span>
        </button>
        {predefinedColors.map((color) => (
          <button
            key={color}
            onClick={() => onColorChange(color)}
            className={`w-6 h-6 rounded border-2 transition-all ${
              selectedColor === color
                ? 'border-violets-are-blue scale-110 ring-2 ring-violets-are-blue/50'
                : 'border-neutral-500 hover:border-neutral-400'
            }`}
            style={{ backgroundColor: color }}
            title={color}
          />
        ))}
      </div>
      {selectedColor && (
        <div className="text-xs text-gray-400">
          Selected: {selectedColor}
        </div>
      )}
    </div>
  );
};

export const VectorImagePanel = ({
  canvas,
  agentId,
  planId,
}: VectorImagePanelProps) => {
  const imagePromptRef = useRef<HTMLTextAreaElement>(null);
  const [imagePrompt, setImagePrompt] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<VectorOptionType | null>(null);
  const [seed, setSeed] = useState(Math.floor(Math.random() * 1000000));
  const [artisticLevel, setArtisticLevel] = useState(3);
  const [selectedColors, setSelectedColors] = useState<string[]>([]);
  const [backgroundColor, setBackgroundColor] = useState<string>('');
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState<'style' | 'details'>('style');
  const { trackContentEvent } = useMixpanelEvent();
  const { activeProject } = useProjectContext();
  const {
    setVectorizing, loadingStates,
  } = useCanvasLoading();

  const hexToRgb = (hex: string): [number, number, number] => {
    const cleanHex = hex.replace('#', '');
    const bigint = parseInt(cleanHex, 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;
    return [r, g, b];
  };

  useEffect(() => {
    if (imagePromptRef?.current) {
      autosize(imagePromptRef.current);
    }
  }, []);

  const handleStyleSelect = (style: VectorOptionType) => {
    setSelectedStyle(style);
    setCurrentStep('details');
  };

  const handleBackToStyles = () => {
    setCurrentStep('style');
  };

  const handleGenerate = async () => {
    if (!imagePrompt.trim()) {
      setError('Please enter an image description');
      return;
    }

    if (imagePrompt.length < 3) {
      setError('Description should be at least 3 characters');
      return;
    }

    if (!agentId) {
      setError('Agent ID is required for image generation');
      return;
    }

    setError('');
    setVectorizing(true);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL || 'http://localhost:2151';
      const endpoint = `${baseUrl}/${agentId}/recraft-image`;

      
      let isLogoStyle = false;

      if (selectedStyle) {
        isLogoStyle = VectorImageStyleGroups.logos.styles.some(
          style => style.option === selectedStyle.option,
        );
      }

      const colorsArray = selectedColors.length > 0
        ? selectedColors.map(color => ({ rgb: hexToRgb(color) }))
        : undefined;

      const backgroundColorObj = backgroundColor
        ? { rgb: hexToRgb(backgroundColor) }
        : undefined;

      const requestBody = {
        prompt: imagePrompt,
        width: 1024,
        height: 1024,
        count: 1,
        model: isLogoStyle ? 'recraftv2' : 'recraftv3',
        style: isLogoStyle ? 'icon' : 'vector_illustration',
        substyle: selectedStyle?.substyle,
        styleId: selectedStyle?.option || '',
        seed: seed,
        controls: {
          artistic_level: isLogoStyle ? undefined : artisticLevel,
          colors: colorsArray,
          background_color: backgroundColorObj,
          no_text: false,
        },
      };

      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 422 && errorData.error?.includes('NSFW')) {
          throw new Error('Content flagged as inappropriate. Please try a different prompt.');
        }
        throw new Error(errorData.error || 'Failed to generate vector image');
      }

      const result = await response.json();

      if (result.success && result.images && result.images.length > 0) {
        const imageUrl = result.images[0];

        const svgResponse = await fetch(imageUrl);
        const svgString = await svgResponse.text();
        await addSvgToCanvas(svgString);
        trackContentEvent('image', {
          prompt: imagePrompt,
          imageStyle: selectedStyle?.option || 'none',
        });

        toast.success('Vector image generated and added to canvas!');
      } else {
        throw new Error('No image data received from Recraft');
      }
    } catch (error: unknown) {
      console.error('Error generating vector image:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate vector image. Please try again.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setVectorizing(false);
    }
  };

  const addSvgToCanvas = async (svgString: string) => {
    if (!canvas) {
      return;
    }

    try {
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
      const svgUrl = URL.createObjectURL(svgBlob);

      const imageObj = new window.Image();
      imageObj.onload = () => {
        const konvaImage = new Konva.Image({
          image: imageObj,
          draggable: true,
        });

        let layer = canvas.findOne('Layer') as Konva.Layer;
        if (!layer) {
          layer = new Konva.Layer();
          canvas.add(layer);
        }

        const canvasWidth = canvas.width();
        const canvasHeight = canvas.height();
        const imageWidth = imageObj.naturalWidth;
        const imageHeight = imageObj.naturalHeight;

        let isLogoStyle = false;
        if (selectedStyle) {
          isLogoStyle = VectorImageStyleGroups.logos.styles.some(
            style => style.option === selectedStyle.option,
          );
        }

        if (isLogoStyle) {
          const logoSize = 256;
          const scaleX = logoSize / imageWidth;
          const scaleY = logoSize / imageHeight;
          const scale = Math.min(scaleX, scaleY);

          konvaImage.scaleX(scale);
          konvaImage.scaleY(scale);
          konvaImage.x((canvasWidth - imageWidth * scale) / 2);
          konvaImage.y((canvasHeight - imageHeight * scale) / 2);
        } else {
          const scaleX = canvasWidth / imageWidth;
          const scaleY = canvasHeight / imageHeight;
          const scale = Math.min(scaleX, scaleY);

          konvaImage.scaleX(scale);
          konvaImage.scaleY(scale);
          konvaImage.x((canvasWidth - imageWidth * scale) / 2);
          konvaImage.y((canvasHeight - imageHeight * scale) / 2);
        }

        addCursorHandlers(konvaImage);

        layer.add(konvaImage);

        let transformer = layer.findOne('Transformer') as Konva.Transformer;
        if (!transformer) {
          transformer = new Konva.Transformer();
          layer.add(transformer);
        }

        transformer.nodes([konvaImage]);
        canvas.batchDraw();
        URL.revokeObjectURL(svgUrl);
      };
      imageObj.src = svgUrl;

      if (activeProject?.project_id && agentId) {
        const fileName = `Vector Image - ${imagePrompt.slice(0, 30)}${imagePrompt.length > 30 ? '...' : ''}`;
        projectImageStorage.addGeneratedImage(
          activeProject.project_id,
          agentId,
          svgUrl,
          fileName,
          planId,
          imagePrompt,
        ).then(() => {
          window.dispatchEvent(new CustomEvent('projectImagesUpdated', { detail: { projectId: activeProject.project_id } }));
        }).catch((error) => {
          console.error('Error storing vector image:', error);
        });
      }

      trackContentEvent('image', {
        prompt: imagePrompt,
        imageStyle: selectedStyle?.option || 'none',
      });
    } catch (error: unknown) {
      console.error('Error adding SVG to canvas:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to add vector image to canvas';
      toast.error(errorMessage);
    }
  };

  return (
    <div className="px-6 py-4">
      {currentStep === 'style' ? (
        <>
          <div className="mb-4">
            <h3 className="text-white font-semibold text-lg">Choose Vector Style</h3>
            <p className="text-gray-400 text-sm">Create vector illustrations using AI</p>
          </div>
          <div className="flex flex-col gap-4">
            <div className='flex-1'>
              <div className="pr-2">
                {Object.entries(VectorImageStyleGroups).map(([groupKey, group], groupIndex) => (
                  <div key={groupKey} className={`${groupIndex > 0 ? 'mt-8' : ''} mb-6`}>
                    <div className="sticky top-0 pt-4 bg-neutral-900 pb-2 mb-3 z-10">
                      <h4 className="text-gray-300 text-xs font-semibold uppercase tracking-wide border-b border-neutral-700 pb-2">
                        {group.title}
                      </h4>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {group.styles.map((style) => (
                        <button
                          key={style.option}
                          onClick={() => handleStyleSelect(style)}
                          className="relative rounded-2xl transition-all duration-300 text-left group aspect-square bg-gradient-to-br from-neutral-800/80 to-neutral-900/80 backdrop-blur-sm hover:from-neutral-700/90 hover:to-neutral-800/90 hover:scale-[1.02] hover:shadow-xl hover:shadow-black/20 active:scale-[0.98]"
                        >
                          <div className="flex flex-col h-full">
                            <div className="flex-1 relative overflow-hidden rounded-xl">
                              <div className="relative h-full w-full rounded-xl overflow-hidden">
                                <Image
                                  src={`/images/vector_styles/${style.option}.png`}
                                  alt={style.label}
                                  width={200}
                                  height={200}
                                  quality={30}
                                  className='rounded-xl h-full w-full object-cover transition-transform duration-300 group-hover:scale-105'
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    target.src = '/images/vector_styles/vectorIllustration.png';
                                  }}
                                />
                                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />
                              </div>
                            </div>
                            <div className="text-[10px] text-center absolute bottom-3 left-3 right-3 text-white bg-black/60 rounded-lg py-1.5 px-2.5 backdrop-blur-sm truncate shadow-lg border border-white/10 group-hover:bg-black/70 transition-all duration-300">
                              {style.label}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      ) : currentStep === 'details' ? (
        <>
          <div className="mb-4">
            <div className="flex items-center gap-3 mb-2">
              <div>
                <h3 className="text-white font-semibold text-lg">Describe Your Vector Image</h3>
                <p className="text-gray-400 text-sm">
                Style: <span className="text-violets-are-blue">{selectedStyle?.label}</span>
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col space-y-4">
            <label htmlFor='image-prompt' className="text-white font-medium text-sm">
              Image Description
              <TextArea
                id="image-prompt"
                name="vector-image-prompt"
                ref={imagePromptRef}
                value={imagePrompt}
                width='w-full'
                onChange={(e) => setImagePrompt(e.target.value)}
                placeholder="Describe the vector image you want to create..."
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey && !loadingStates.isGenerating && imagePrompt.trim()) {
                    e.preventDefault();
                    handleGenerate();
                  }
                }}
              />
            </label>

            <div>
              <label className="text-white text-sm font-medium mb-2 flex justify-between">
                <span>Seed: {seed}</span>
                <Button
                  variant='outline-rounded'
                  size="xs"
                  onClick={() => setSeed(Math.floor(Math.random() * 1000000))}
                >
                  Random
                </Button>
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="range"
                  min="1"
                  max="1000000"
                  value={seed}
                  onChange={(e) => setSeed(Number(e.target.value))}
                  className="flex-1 accent-violets-are-blue"
                />
              </div>
            </div>
            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Artistic Level: {artisticLevel}
              </label>
              <input
                type="range"
                min="0"
                max="5"
                step="1"
                value={artisticLevel}
                onChange={(e) => setArtisticLevel(Number(e.target.value))}
                className="w-full accent-violets-are-blue"
              />
            </div>

            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Preferred Colors (Optional)
              </label>
              <ColorSelector
                selectedColors={selectedColors}
                onColorsChange={setSelectedColors}
                maxColors={5}
              />
            </div>

            <div>
              <label className="text-white text-sm font-medium mb-2 block">
                Background Color (Optional)
              </label>
              <BackgroundColorSelector
                selectedColor={backgroundColor}
                onColorChange={setBackgroundColor}
              />
            </div>

            {error && (
              <div className="text-tulip text-sm">
                {error}
              </div>
            )}

            <Button
              variant="gradient"
              size="md"
              width="w-full"
              onClick={handleGenerate}
              disabled={loadingStates.isVectorizing || !imagePrompt.trim()}
            >
              {loadingStates.isVectorizing ? 'Generating Vector Image...' : 'Generate Vector Image'}
            </Button>
            <Button
              onClick={handleBackToStyles}
              variant='outline'
              size='md'
              width='w-full'
            >
              Back
            </Button>
          </div>
        </>
      ) : null}
    </div>
  );
};
